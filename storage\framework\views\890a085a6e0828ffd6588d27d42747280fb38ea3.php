

<?php $__env->startSection('title', 'จัดการข้อความติดต่อ - Admin Panel'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-envelope me-2"></i>จัดการข้อความติดต่อ
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">แดชบอร์ด</a></li>
                        <li class="breadcrumb-item active">จัดการข้อความติดต่อ</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">รายการข้อความติดต่อทั้งหมด</h3>
                            <div class="card-tools">
                                <div class="btn-group">
                                    <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown">
                                        <i class="fas fa-filter me-1"></i>กรองข้อมูล
                                    </button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" href="<?php echo e(route('admin.contacts.index')); ?>">ทั้งหมด</a>
                                        <a class="dropdown-item" href="<?php echo e(route('admin.contacts.index', ['filter' => 'unread'])); ?>">ยังไม่อ่าน</a>
                                        <a class="dropdown-item" href="<?php echo e(route('admin.contacts.index', ['filter' => 'read'])); ?>">อ่านแล้ว</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if(session('success')): ?>
                                <div class="alert alert-success alert-dismissible">
                                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                                    <?php echo e(session('success')); ?>

                                </div>
                            <?php endif; ?>

                            <?php if($contacts->count() > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th style="width: 40px;">สถานะ</th>
                                                <th>ชื่อ</th>
                                                <th>ติดต่อ</th>
                                                <th>ข้อความ</th>
                                                <th style="width: 120px;">วันที่</th>
                                                <th style="width: 150px;">จัดการ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $contacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr class="<?php echo e(!$contact->is_read ? 'table-warning' : ''); ?>">
                                                    <td class="text-center">
                                                        <?php if($contact->is_read): ?>
                                                            <i class="fas fa-envelope-open text-success" title="อ่านแล้ว"></i>
                                                        <?php else: ?>
                                                            <i class="fas fa-envelope text-warning" title="ยังไม่อ่าน"></i>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <strong><?php echo e($contact->name); ?></strong>
                                                        <?php if(!$contact->is_read): ?>
                                                            <span class="badge badge-warning ml-1">ใหม่</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if($contact->phone): ?>
                                                            <div><i class="fas fa-phone text-primary"></i> <?php echo e($contact->phone); ?></div>
                                                        <?php endif; ?>
                                                        <?php if($contact->email): ?>
                                                            <div><i class="fas fa-envelope text-info"></i> <?php echo e($contact->email); ?></div>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php echo e(Str::limit($contact->message, 60)); ?>

                                                    </td>
                                                    <td class="text-center">
                                                        <small><?php echo e($contact->created_at->format('d/m/Y')); ?></small><br>
                                                        <small class="text-muted"><?php echo e($contact->created_at->format('H:i')); ?></small>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="<?php echo e(route('admin.contacts.edit', $contact)); ?>"
                                                               class="btn btn-sm btn-info"
                                                               title="ดูรายละเอียด">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <?php if(!$contact->is_read): ?>
                                                                <form action="<?php echo e(route('admin.contacts.update', $contact)); ?>"
                                                                      method="POST"
                                                                      style="display:inline;">
                                                                    <?php echo csrf_field(); ?> <?php echo method_field('PUT'); ?>
                                                                    <input type="hidden" name="is_read" value="1">
                                                                    <button type="submit" class="btn btn-sm btn-success" title="ทำเครื่องหมายว่าอ่านแล้ว">
                                                                        <i class="fas fa-check"></i>
                                                                    </button>
                                                                </form>
                                                            <?php endif; ?>
                                                            <button type="button" class="btn btn-sm btn-danger delete-contact"
                                                                    data-contact-id="<?php echo e($contact->id); ?>"
                                                                    data-contact-name="<?php echo e($contact->name); ?>"
                                                                    title="ลบ">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">ยังไม่มีข้อความติดต่อ</h5>
                                    <p class="text-muted">เมื่อมีผู้ติดต่อเข้ามา ข้อความจะแสดงที่นี่</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Delete contact
    $('.delete-contact').on('click', function(e) {
        e.preventDefault();
        let contactId = $(this).data('contact-id');
        let contactName = $(this).data('contact-name');

        Swal.fire({
            title: 'ลบข้อความติดต่อ?',
            html: `คุณแน่ใจหรือไม่ที่จะลบข้อความจาก<br><strong>"${contactName}"</strong>?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '<i class="fas fa-trash"></i> ลบเลย',
            cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                // แสดง loading
                Swal.fire({
                    title: 'กำลังลบ...',
                    text: 'กรุณารอสักครู่',
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // สร้าง form และ submit
                let form = $('<form>', {
                    'method': 'POST',
                    'action': `<?php echo e(url('admin/contacts')); ?>/${contactId}`
                });

                form.append($('<input>', {
                    'type': 'hidden',
                    'name': '_token',
                    'value': $('meta[name="csrf-token"]').attr('content')
                }));

                form.append($('<input>', {
                    'type': 'hidden',
                    'name': '_method',
                    'value': 'DELETE'
                }));

                $('body').append(form);
                form.submit();
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/contacts/index.blade.php ENDPATH**/ ?>